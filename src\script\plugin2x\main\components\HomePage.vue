<template>
    <div class="home-page">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo-section">
                    <img src="~@/img/main/logo.png" alt="logo" class="logo" />
                    <img src="~@/img/main/main-title.png" alt="logo-title" class="title" />
                    <div class="collapse-btn" @click="handleCollapse"></div>
                </div>
                <div class="new-chat">
                    <div class="new-chat-icon"></div>
                    <span class="new-chat-text">新对话</span>
                </div>
            </div>

            <div class="search-section">
                <el-input
                    v-model="searchText"
                    placeholder="搜索"
                    prefix-icon="el-icon-search"
                    clearable
                    class="search-input"
                    @input="handleSearch"
                />
            </div>

            <div class="menu-section custom-scrollbar">
                <template v-for="menuGroup in filteredMenuGroups">
                    <div class="menu-item" :key="`title-${menuGroup.key}`">
                        <span class="menu-title">{{ menuGroup.title }}</span>
                    </div>
                    <div class="menu-group" :key="`group-${menuGroup.key}`">
                        <div
                            class="menu-group-item"
                            :class="{ active: activeMenuItem === item.id }"
                            v-for="item in menuGroup.items"
                            :key="item.id"
                            @click="handleMenuItemClick(item)"
                        >
                            <span class="menu-group-text">{{ item.text }}</span>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-icon">
                        <div class="chat-bubble">
                            <i class="el-icon-chat-dot-round"></i>
                        </div>
                    </div>
                    <h1 class="welcome-title">{{ welcomeConfig.title }}</h1>
                    <p class="welcome-desc">{{ welcomeConfig.description }}</p>
                </div>

                <!-- 功能卡片区域 -->
                <div class="feature-cards">
                    <el-card
                        class="feature-card"
                        v-for="(card, index) in featureCards"
                        :key="index"
                        shadow="hover"
                        @click.native="handleCardClick(card)"
                    >
                        <div class="card-content">
                            <div class="card-icon">
                                <img :src="card.icon" :alt="card.title" />
                            </div>
                            <h3 class="card-title">{{ card.title }}</h3>
                            <p class="card-desc">{{ card.description }}</p>
                        </div>
                    </el-card>
                </div>

                <!-- 输入区域 -->
                <div class="input-section">
                    <div class="input-container">
                        <el-input
                            v-model="inputText"
                            type="textarea"
                            :placeholder="inputConfig.placeholder"
                            :autosize="{ minRows: 1, maxRows: 4 }"
                            resize="none"
                            @keydown.native="handleKeydown"
                            class="chat-input"
                        />
                        <div class="input-actions">
                            <el-select v-model="selectedModel" size="small" class="model-select">
                                <el-option
                                    v-for="model in modelOptions"
                                    :key="model.value"
                                    :label="model.label"
                                    :value="model.value"
                                />
                            </el-select>
                            <el-button
                                type="primary"
                                icon="el-icon-s-promotion"
                                circle
                                size="small"
                                @click="sendMessage"
                                :disabled="!inputText.trim()"
                                class="send-btn"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HomePage',
    data() {
        return {
            searchText: '',
            inputText: '',
            selectedModel: 'deepseek-v3',
            filteredMenuItems: {},

            welcomeConfig: {
                title: '你好，欢迎使用全新智能助手',
                description:
                    '智能为您提供帮助，请您输入了解不同使用场景的需求，用户可以根据您的需求进行相关问答'
            },

            inputConfig: {
                placeholder: '请输入您的问题，Shift+Enter换行，或者按回车键发送您的问题'
            },

            modelOptions: [
                { label: 'DeepSeek V3', value: 'deepseek-v3' },
                { label: 'GPT-4', value: 'gpt-4' },
                { label: 'Claude-3', value: 'claude-3' }
            ],

            activeMenuItem: null,
            menuGroups: [
                {
                    key: 'today',
                    title: '今天',
                    items: [
                        { id: 1, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 2, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 3, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 4, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'yesterday',
                    title: '昨天',
                    items: [
                        { id: 6, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 7, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 8, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 9, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'week',
                    title: '一周内',
                    items: [
                        { id: 10, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 11, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month',
                    title: '30天内',
                    items: [
                        { id: 12, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 13, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 14, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 15, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                },
                {
                    key: 'month-3',
                    title: '90天内',
                    items: [
                        { id: 16, text: 'API接口数据分析报表（一）', type: 'conversation' },
                        { id: 17, text: '数据库性能监控和优化建议', type: 'conversation' },
                        { id: 18, text: '对数据库设计的建议', type: 'conversation' },
                        { id: 19, text: '对数据库设计的建议和优化建议', type: 'conversation' }
                    ]
                }
            ],

            featureCards: [
                {
                    id: 'location-capability',
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据您的需求为您提供（定位、导航及其他）、又或者是AGPS、支持导航、车载导航、地理位置信息等功能的推荐。',
                    action: 'recommend-location'
                },
                {
                    id: 'asset-subscription',
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订阅问答',
                    description:
                        '为您提供资源、主要用于订阅服务的问答，资产内容（如数据资产管理等等），帮助您可以更好的了解订阅问答。',
                    action: 'asset-qa'
                },
                {
                    id: 'business-requirements',
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求数据',
                    description:
                        '建议您了解各种内的问题，包括技术支持、数据管理和调试问题、心理支持、支持上述工作需求数据问题。',
                    action: 'business-data'
                }
            ]
        };
    },

    computed: {
        // 根据搜索文本过滤菜单项
        filteredMenuGroups() {
            if (!this.searchText.trim()) {
                return this.menuGroups;
            }

            return this.menuGroups
                .map((group) => ({
                    ...group,
                    items: group.items.filter((item) =>
                        item.text.toLowerCase().includes(this.searchText.toLowerCase())
                    )
                }))
                .filter((group) => group.items.length > 0);
        }
    },

    methods: {
        // 处理侧边栏收起
        handleCollapse() {
            this.$emit('sidebar-collapse');
            console.log('侧边栏收起');
        },

        // 处理搜索输入
        handleSearch(value) {
            console.log('搜索:', value);
        },

        // 获取过滤后的菜单项
        getFilteredItems(items) {
            if (!this.searchText.trim()) {
                return items;
            }
            return items.filter((item) =>
                item.text.toLowerCase().includes(this.searchText.toLowerCase())
            );
        },

        // 处理菜单项点击
        handleMenuItemClick(item) {
            console.log('点击菜单项:', item);
            this.activeMenuItem = item.id;
            this.$emit('menu-item-click', item);
        },

        // 处理功能卡片点击
        handleCardClick(card) {
            console.log('点击功能卡片:', card);
            this.$emit('card-click', card);

            // 根据卡片类型设置预设问题
            const presetQuestions = {
                'location-capability': '请为我推荐位置能力使用方案',
                'asset-subscription': '我想了解位置资产订阅相关问题',
                'business-requirements': '请帮我分析位置业务需求数据'
            };

            if (presetQuestions[card.id]) {
                this.inputText = presetQuestions[card.id];
            }
        },

        // 处理键盘事件
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        },

        // 发送消息
        sendMessage() {
            if (this.inputText.trim()) {
                const message = {
                    text: this.inputText.trim(),
                    model: this.selectedModel,
                    timestamp: new Date().toISOString()
                };

                console.log('发送消息:', message);
                this.$emit('send-message', message);

                // 清空输入框
                this.inputText = '';
            }
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
}

// 侧边栏样式
.sidebar {
    width: 18.75rem;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    height: 100vh;

    .sidebar-header {
        padding: 1.75rem 0.75rem 0;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.625rem;

            .logo {
                width: 2.25rem;
                height: 2.25rem;
            }

            .title {
                width: 10rem;
                height: 1.5rem;
            }

            .collapse-btn {
                margin-left: auto;
                width: 1.5rem;
                height: 1.5rem;
                background-image: url('~@/img/main/sidebar-collapse.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                cursor: pointer;
            }
        }
        .new-chat {
            height: 2.5rem;
            background: rgba(2, 101, 254, 0.05);
            border-radius: 0.5rem;
            border: 0.0625rem solid rgba(2, 101, 254, 0.5);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 0.75rem;
            cursor: pointer;
            user-select: none;
            &-icon {
                width: 1.25rem;
                height: 1.25rem;
                background-image: url('~@/img/main/new-chat.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }
            &-text {
                font-weight: 500;
                font-size: 0.875rem;
                color: #0265fe;
                line-height: 1.25rem;
            }
        }
    }

    .search-section {
        padding: 1.5rem 0.75rem 1.875rem;
        /deep/.el-input.search-input {
            .el-input__inner {
                height: 2.25rem;
                background: #ffffff;
                border-radius: 0.5rem;
                border: 0.0625rem solid rgba(213, 214, 216, 0.5);
            }
        }
    }

    .menu-section {
        min-height: 0;
        flex: 1;
        padding: 0 0.75rem 1.5rem;
        overflow-y: auto;
        --thumb-color: rgba(0, 0, 0, 0.1);

        .menu-item {
            padding: 0 0.75rem 0.5rem;
            .menu-title {
                font-weight: 500;
                font-size: 0.875rem;
                color: #666666;
                line-height: 1.25rem;
            }
        }

        .menu-group {
            margin-bottom: 1.875rem;

            .menu-group-item {
                padding: 0.5rem 0.75rem;
                cursor: pointer;
                transition: background-color 0.2s ease;
                border-radius: 0.5rem;
                &:hover {
                    background: #f3f4f6;
                }
                &.active {
                    background: #;
                    &:hover {
                        background: #d3d4d6;
                    }
                }
            }
            .menu-group-text {
                font-size: 0.875rem;
                color: #374151;
                display: block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}

// 主内容区域样式
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-width: 50rem;
        margin: 0 auto;
        padding: 2.5rem 1.25rem;
        width: 100%;

        .welcome-section {
            text-align: center;
            margin-bottom: 2.5rem;

            .welcome-icon {
                margin-bottom: 1.5rem;

                .chat-bubble {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 3.75rem;
                    height: 3.75rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    font-size: 1.5rem;
                    color: white;
                }
            }

            .welcome-title {
                font-size: 2rem;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 1rem 0;
                line-height: 1.2;
            }

            .welcome-desc {
                font-size: 1rem;
                color: #6b7280;
                line-height: 1.6;
                max-width: 37.5rem;
                margin: 0 auto;
            }
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(15.625rem, 1fr));
            gap: 1.5rem;
            margin-bottom: 2.5rem;

            .feature-card {
                cursor: pointer;
                transition: all 0.3s ease;
                border-radius: 0.75rem !important;
                &:hover {
                    transform: translateY(-0.125rem);
                }

                .card-content {
                    padding: 1.5rem;

                    .card-icon {
                        margin-bottom: 1rem;

                        img {
                            width: 3rem;
                            height: 3rem;
                        }
                    }

                    .card-title {
                        font-size: 1.125rem;
                        font-weight: 600;
                        color: #1f2937;
                        margin: 0 0 0.75rem 0;
                        line-height: 1.3;
                    }

                    .card-desc {
                        font-size: 0.875rem;
                        color: #6b7280;
                        line-height: 1.5;
                        margin: 0;
                    }
                }
            }
        }

        .input-section {
            margin-top: auto;
            padding-top: 1.25rem;

            .input-container {
                display: flex;
                align-items: flex-end;
                background: white;
                border: 0.0625rem solid #d1d5db;
                border-radius: 0.5rem;
                padding: 0.75rem 1rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
                gap: 0.75rem;

                .chat-input {
                    flex: 1;

                    /deep/ .el-textarea__inner {
                        border: none !important;
                        padding: 0 !important;
                        box-shadow: none !important;
                        resize: none !important;
                        font-size: 0.875rem;
                        line-height: 1.5;

                        &:focus {
                            border: none !important;
                            box-shadow: none !important;
                        }
                    }
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    .model-select {
                        width: 7.5rem;
                    }

                    .send-btn {
                        width: 2rem !important;
                        height: 2rem !important;
                        padding: 0 !important;

                        &.is-disabled {
                            opacity: 0.5;
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }
    }
}
</style>
