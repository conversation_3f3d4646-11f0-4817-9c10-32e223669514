<template>
    <div class="home-page">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo-section">
                    <img src="~@/img/main/logo.png" alt="logo" class="logo" />
                    <span class="title">智能使用数据助手</span>
                    <button class="collapse-btn">
                        <img src="~@/img/main/sidebar-collapse.png" alt="收起" />
                    </button>
                </div>
            </div>

            <div class="search-section">
                <div class="search-box">
                    <input type="text" placeholder="搜索" v-model="searchText" />
                    <i class="search-icon">🔍</i>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-item">
                    <span class="menu-title">今天</span>
                </div>

                <div class="menu-group">
                    <div class="menu-item" v-for="item in todayItems" :key="item.id">
                        <span class="menu-text">{{ item.text }}</span>
                    </div>
                </div>

                <div class="menu-item">
                    <span class="menu-title">昨天</span>
                </div>

                <div class="menu-group">
                    <div class="menu-item" v-for="item in yesterdayItems" :key="item.id">
                        <span class="menu-text">{{ item.text }}</span>
                    </div>
                </div>

                <div class="menu-item">
                    <span class="menu-title">一周内</span>
                </div>

                <div class="menu-group">
                    <div class="menu-item" v-for="item in weekItems" :key="item.id">
                        <span class="menu-text">{{ item.text }}</span>
                    </div>
                </div>

                <div class="menu-item">
                    <span class="menu-title">30天内</span>
                </div>

                <div class="menu-group">
                    <div class="menu-item" v-for="item in monthItems" :key="item.id">
                        <span class="menu-text">{{ item.text }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <div class="welcome-icon">
                        <div class="chat-bubble">💬</div>
                    </div>
                    <h1 class="welcome-title">你好，欢迎使用全新智能助手</h1>
                    <p class="welcome-desc">
                        智能为您提供帮助，请您输入了解不同使用场景的需求，用户可以根据您的需求进行相关问答
                    </p>
                </div>

                <!-- 功能卡片区域 -->
                <div class="feature-cards">
                    <div class="feature-card" v-for="(card, index) in featureCards" :key="index">
                        <div class="card-icon">
                            <img :src="card.icon" :alt="card.title" />
                        </div>
                        <h3 class="card-title">{{ card.title }}</h3>
                        <p class="card-desc">{{ card.description }}</p>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-section">
                    <div class="input-container">
                        <input
                            type="text"
                            placeholder="请输入您的问题，Shift+Enter换行，或者按回车键发送您的问题"
                            v-model="inputText"
                            @keydown="handleKeydown"
                            class="chat-input"
                        />
                        <div class="input-actions">
                            <select v-model="selectedModel" class="model-select">
                                <option value="DeepSeek V3">DeepSeek V3</option>
                            </select>
                            <button class="send-btn" @click="sendMessage">
                                <span>📤</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'HomePage',
    data() {
        return {
            searchText: '',
            inputText: '',
            selectedModel: 'DeepSeek V3',
            todayItems: [
                { id: 1, text: 'API接口数据分析报表（一）' },
                { id: 2, text: '数据库性能监控和优化建议' },
                { id: 3, text: '对数据库设计的建议' },
                { id: 4, text: '对数据库设计的建议和优化建议' }
            ],
            yesterdayItems: [
                { id: 5, text: '形式' },
                { id: 6, text: 'API接口数据分析报表（一）' },
                { id: 7, text: '数据库性能监控和优化建议' },
                { id: 8, text: '对数据库设计的建议' },
                { id: 9, text: '对数据库设计的建议和优化建议' }
            ],
            weekItems: [
                { id: 10, text: '对数据库设计的建议' },
                { id: 11, text: '对数据库设计的建议和优化建议' }
            ],
            monthItems: [
                { id: 12, text: 'API接口数据分析报表（一）' },
                { id: 13, text: '数据库性能监控和优化建议' },
                { id: 14, text: '对数据库设计的建议' },
                { id: 15, text: '对数据库设计的建议和优化建议' }
            ],
            featureCards: [
                {
                    icon: require('@/img/main/card-logo-1.png'),
                    title: '位置能力使用推荐',
                    description:
                        '根据您的需求为您提供（定位、导航及其他）、又或者是AGPS、支持导航、车载导航、地理位置信息等功能的推荐。'
                },
                {
                    icon: require('@/img/main/card-logo-2.png'),
                    title: '位置资产订阅问答',
                    description:
                        '为您提供资源、主要用于订阅服务的问答，资产内容（如数据资产管理等等），帮助您可以更好的了解订阅问答。'
                },
                {
                    icon: require('@/img/main/card-logo-3.png'),
                    title: '位置业务需求数据',
                    description:
                        '建议您了解各种内的问题，包括技术支持、数据管理和调试问题、心理支持、支持上述工作需求数据问题。'
                }
            ]
        };
    },
    methods: {
        handleKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        },
        sendMessage() {
            if (this.inputText.trim()) {
                console.log('发送消息:', this.inputText);
                // 这里可以添加发送消息的逻辑
                this.inputText = '';
            }
        }
    }
};
</script>

<style scoped lang="less">
.home-page {
    display: flex;
    height: 100vh;
    background-image: url('~@/img/main/main-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 侧边栏样式
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    border-right: 1px solid #e5e5e5;
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-y: auto;

    .sidebar-header {
        padding: 16px;
        border-bottom: 1px solid #e5e5e5;

        .logo-section {
            display: flex;
            align-items: center;
            gap: 8px;

            .logo {
                width: 24px;
                height: 24px;
            }

            .title {
                font-size: 14px;
                font-weight: 500;
                color: #333;
                flex: 1;
            }

            .collapse-btn {
                background: none;
                border: none;
                cursor: pointer;
                padding: 4px;

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    .search-section {
        padding: 16px;
        border-bottom: 1px solid #e5e5e5;

        .search-box {
            position: relative;

            input {
                width: 100%;
                padding: 8px 32px 8px 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background: #f9fafb;

                &::placeholder {
                    color: #9ca3af;
                }

                &:focus {
                    outline: none;
                    border-color: #3b82f6;
                    background: white;
                }
            }

            .search-icon {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                color: #9ca3af;
                font-size: 14px;
            }
        }
    }

    .menu-section {
        flex: 1;
        padding: 8px 0;

        .menu-item {
            padding: 8px 16px;
            cursor: pointer;

            &:hover {
                background: #f3f4f6;
            }

            .menu-title {
                font-size: 12px;
                font-weight: 600;
                color: #6b7280;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            .menu-text {
                font-size: 14px;
                color: #374151;
                display: block;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .menu-group {
            margin-bottom: 16px;

            .menu-item {
                padding-left: 24px;

                &:hover {
                    background: #f3f4f6;
                }
            }
        }
    }
}

// 主内容区域样式
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-width: 800px;
        margin: 0 auto;
        padding: 40px 20px;
        width: 100%;

        .welcome-section {
            text-align: center;
            margin-bottom: 40px;

            .welcome-icon {
                margin-bottom: 24px;

                .chat-bubble {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 50%;
                    font-size: 24px;
                    color: white;
                }
            }

            .welcome-title {
                font-size: 32px;
                font-weight: 600;
                color: #1f2937;
                margin: 0 0 16px 0;
                line-height: 1.2;
            }

            .welcome-desc {
                font-size: 16px;
                color: #6b7280;
                line-height: 1.6;
                max-width: 600px;
                margin: 0 auto;
            }
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 40px;

            .feature-card {
                background: white;
                border-radius: 12px;
                padding: 24px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                cursor: pointer;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
                }

                .card-icon {
                    margin-bottom: 16px;

                    img {
                        width: 48px;
                        height: 48px;
                    }
                }

                .card-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1f2937;
                    margin: 0 0 12px 0;
                    line-height: 1.3;
                }

                .card-desc {
                    font-size: 14px;
                    color: #6b7280;
                    line-height: 1.5;
                    margin: 0;
                }
            }
        }

        .input-section {
            margin-top: auto;
            padding-top: 20px;

            .input-container {
                display: flex;
                align-items: center;
                background: white;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                padding: 8px 16px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

                .chat-input {
                    flex: 1;
                    border: none;
                    padding: 8px 0;
                    font-size: 14px;

                    &:focus {
                        outline: none;
                    }

                    &::placeholder {
                        color: #9ca3af;
                    }
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .model-select {
                        padding: 6px 8px;
                        border: 1px solid #d1d5db;
                        border-radius: 4px;
                        font-size: 14px;
                        background: #f9fafb;
                        color: #374151;

                        &:focus {
                            outline: none;
                            border-color: #3b82f6;
                        }
                    }

                    .send-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 32px;
                        height: 32px;
                        border-radius: 50%;
                        background: #3b82f6;
                        color: white;
                        border: none;
                        cursor: pointer;
                        transition: background 0.2s ease;

                        &:hover {
                            background: #2563eb;
                        }
                    }
                }
            }
        }
    }
}
